using System.ComponentModel.DataAnnotations;

namespace GMCadiomMeeting.Shared.ViewModels.Meeting.JoinMeeting;

/// <summary>
/// Request model for joining a meeting
/// </summary>
public class JoinMeetingRequest
{
    public int UserId { get; set; }
    public string? IpAddress { get; set; }
    public string? UserAgent { get; set; }

    /// <summary>
    /// Meeting code or ID
    /// </summary>
    [Required(ErrorMessage = "Meeting code is required")]
    [Display(Name = "Meeting Code")]
    [RegularExpression(@"^\d{3}-\d{3}-\d{3}$", ErrorMessage = "Meeting code must be in format XXX-XXX-XXX")]
    public string MeetingCode { get; set; } = string.Empty;

    /// <summary>
    /// Meeting password (if required)
    /// </summary>
    [Display(Name = "Meeting Password")]
    [DataType(DataType.Password)]
    public string Password { get; set; } = string.Empty;

    /// <summary>
    /// Display name for the participant
    /// </summary>
    [Required(ErrorMessage = "Display name is required")]
    [StringLength(100, MinimumLength = 2, ErrorMessage = "Display name must be between 2 and 100 characters")]
    [Display(Name = "Your Name")]
    public string DisplayName { get; set; } = string.Empty;

    /// <summary>
    /// Indicates if camera is enabled
    /// </summary>
    [Display(Name = "Join with Camera On")]
    public bool IsCameraEnabled { get; set; } = true;

    /// <summary>
    /// Indicates if microphone is enabled
    /// </summary>
    [Display(Name = "Join with Microphone On")]
    public bool IsMicrophoneEnabled { get; set; } = false;
}
