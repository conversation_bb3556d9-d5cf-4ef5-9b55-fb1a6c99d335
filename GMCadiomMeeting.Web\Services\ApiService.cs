using GMCadiomMeeting.Shared.Models;
using GMCadiomMeeting.Shared.ViewModels.Meeting;
using GMCadiomMeeting.Shared.ViewModels.Meeting.JoinMeeting;
using GMCadiomMeeting.Shared.ViewModels.Participant;
using GMCadiomMeeting.Shared.ViewModels.User;
using GMCadiomMeeting.Shared.ViewModels.User.Login;
using GMCadiomMeeting.Web.Models;
using Newtonsoft.Json;
using System.Text;

namespace GMCadiomMeeting.Web.Services;

public interface IApiService
{
    Task<LoginResponse?> LoginAsync(LoginViewModel model);
    Task<UserDto?> RegisterAsync(RegisterViewModel model);
    Task<UserDto?> GetUserAsync(int userId);
    Task<List<MeetingDto>> GetUserMeetingsAsync(int userId);
    Task<MeetingDto?> GetMeetingAsync(int meetingId);
    Task<MeetingDto?> CreateMeetingAsync(CreateMeetingViewModel model, int hostUserId);
    Task<MeetingParticipantDto?> JoinMeetingAsync(JoinMeetingRequest model);
    Task<List<InvitationDto>> GetUserInvitationsAsync(int userId);
    Task<List<InvitationDto>> GetMeetingInvitationsAsync(int meetingId);
    Task<bool> SendInvitationsAsync(SendInvitationViewModel model, int sentByUserId);
    Task<bool> RespondToInvitationAsync(int invitationId, bool accept);
    Task<List<ChatMessageDto>> GetMeetingMessagesAsync(int meetingId, int userId, int page = 1, int pageSize = 50);
    Task<ChatMessageDto?> SendChatMessageAsync(int meetingId, int senderId, string content, int? recipientId = null);
}

public class ApiService : IApiService
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<ApiService> _logger;
    private readonly IConfiguration _configuration;

    public ApiService(HttpClient httpClient, ILogger<ApiService> logger, IConfiguration configuration)
    {
        _httpClient = httpClient;
        _logger = logger;
        _configuration = configuration;

        var apiBaseUrl = _configuration["ApiSettings:BaseUrl"] ?? "https://localhost:7000";
        _httpClient.BaseAddress = new Uri(apiBaseUrl);
        _httpClient.DefaultRequestHeaders.Add("Accept", "application/json");
    }

    public async Task<LoginResponse?> LoginAsync(LoginViewModel model)
    {
        try
        {
            var json = JsonConvert.SerializeObject(new { email = model.Email, password = model.Password });
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync("/api/users/login", content);

            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                return JsonConvert.DeserializeObject<LoginResponse>(responseContent);
            }

            _logger.LogWarning("Login failed for user {Email}. Status: {StatusCode}", model.Email, response.StatusCode);
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during login for user {Email}", model.Email);
            return null;
        }
    }

    public async Task<UserDto?> RegisterAsync(RegisterViewModel model)
    {
        try
        {
            var json = JsonConvert.SerializeObject(new
            {
                email = model.Email,
                password = model.Password,
                displayName = model.DisplayName,
                firstName = model.FirstName,
                lastName = model.LastName,
                timeZone = model.TimeZone
            });
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync("/api/users/register", content);

            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                return JsonConvert.DeserializeObject<UserDto>(responseContent);
            }

            _logger.LogWarning("Registration failed for user {Email}. Status: {StatusCode}", model.Email, response.StatusCode);
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during registration for user {Email}", model.Email);
            return null;
        }
    }

    public async Task<UserDto?> GetUserAsync(int userId)
    {
        try
        {
            var response = await _httpClient.GetAsync($"/api/users/{userId}");

            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                return JsonConvert.DeserializeObject<UserDto>(responseContent);
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting user {UserId}", userId);
            return null;
        }
    }

    public async Task<List<MeetingDto>> GetUserMeetingsAsync(int userId)
    {
        try
        {
            var response = await _httpClient.GetAsync($"/api/meetings/user/{userId}");

            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                return JsonConvert.DeserializeObject<List<MeetingDto>>(responseContent) ?? new List<MeetingDto>();
            }

            return new List<MeetingDto>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting meetings for user {UserId}", userId);
            return new List<MeetingDto>();
        }
    }

    public async Task<MeetingDto?> GetMeetingAsync(int meetingId)
    {
        try
        {
            var response = await _httpClient.GetAsync($"/api/meetings/{meetingId}");

            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                return JsonConvert.DeserializeObject<MeetingDto>(responseContent);
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting meeting {MeetingId}", meetingId);
            return null;
        }
    }

    public async Task<MeetingDto?> CreateMeetingAsync(CreateMeetingViewModel model, int hostUserId)
    {
        try
        {
            var json = JsonConvert.SerializeObject(new
            {
                title = model.Title,
                description = model.Description,
                type = (int)model.Type,
                scheduledStartTime = model.ScheduledStartTime,
                scheduledEndTime = model.ScheduledEndTime,
                hostUserId = hostUserId,
                maxParticipants = model.MaxParticipants,
                password = model.Password,
                isRecordingEnabled = model.IsRecordingEnabled,
                isRecurring = model.IsRecurring,
                recurrencePattern = model.RecurrencePattern
            });
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync("/api/meetings", content);

            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                return JsonConvert.DeserializeObject<MeetingDto>(responseContent);
            }

            _logger.LogWarning("Meeting creation failed. Status: {StatusCode}", response.StatusCode);
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating meeting");
            return null;
        }
    }

    public async Task<MeetingParticipantDto?> JoinMeetingAsync(JoinMeetingRequest request)
    {
        try
        {
            var json = JsonConvert.SerializeObject(request);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync($"/api/meetings/{request.MeetingCode}/join", content);

            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                return JsonConvert.DeserializeObject<MeetingParticipantDto>(responseContent);
            }

            _logger.LogWarning("Join meeting failed for code {MeetingCode}. Status: {StatusCode}", request.MeetingCode, response.StatusCode);
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error joining meeting {MeetingCode}", request.MeetingCode);
            return null;
        }
    }

    public async Task<List<InvitationDto>> GetUserInvitationsAsync(int userId)
    {
        try
        {
            var response = await _httpClient.GetAsync($"/api/invitations/user/{userId}");

            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                return JsonConvert.DeserializeObject<List<InvitationDto>>(responseContent) ?? new List<InvitationDto>();
            }

            return new List<InvitationDto>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting invitations for user {UserId}", userId);
            return new List<InvitationDto>();
        }
    }

    public async Task<List<InvitationDto>> GetMeetingInvitationsAsync(int meetingId)
    {
        try
        {
            var response = await _httpClient.GetAsync($"/api/invitations/meeting/{meetingId}");

            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                return JsonConvert.DeserializeObject<List<InvitationDto>>(responseContent) ?? new List<InvitationDto>();
            }

            return new List<InvitationDto>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting invitations for meeting {MeetingId}", meetingId);
            return new List<InvitationDto>();
        }
    }

    public async Task<bool> SendInvitationsAsync(SendInvitationViewModel model, int sentByUserId)
    {
        try
        {
            var emails = model.EmailAddresses.Split('\n', StringSplitOptions.RemoveEmptyEntries)
                .Select(email => email.Trim())
                .Where(email => !string.IsNullOrEmpty(email))
                .ToList();

            var invitees = emails.Select(email => new
            {
                email = email,
                role = (int)model.InvitedRole
            }).ToList();

            var json = JsonConvert.SerializeObject(new
            {
                meetingId = model.MeetingId,
                sentByUserId = sentByUserId,
                invitees = invitees,
                personalMessage = model.PersonalMessage,
                expiresAt = model.ExpiresAt,
                allowJoinBeforeHost = model.AllowJoinBeforeHost
            });
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync("/api/invitations/send", content);

            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending invitations for meeting {MeetingId}", model.MeetingId);
            return false;
        }
    }

    public async Task<bool> RespondToInvitationAsync(int invitationId, bool accept)
    {
        try
        {
            var json = JsonConvert.SerializeObject(new { accept = accept });
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync($"/api/invitations/{invitationId}/respond", content);

            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error responding to invitation {InvitationId}", invitationId);
            return false;
        }
    }

    public async Task<List<ChatMessageDto>> GetMeetingMessagesAsync(int meetingId, int userId, int page = 1, int pageSize = 50)
    {
        try
        {
            var response = await _httpClient.GetAsync($"/api/chat/meeting/{meetingId}?userId={userId}&page={page}&pageSize={pageSize}");

            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                return JsonConvert.DeserializeObject<List<ChatMessageDto>>(responseContent) ?? new List<ChatMessageDto>();
            }

            return new List<ChatMessageDto>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting messages for meeting {MeetingId}", meetingId);
            return new List<ChatMessageDto>();
        }
    }

    public async Task<ChatMessageDto?> SendChatMessageAsync(int meetingId, int senderId, string content, int? recipientId = null)
    {
        try
        {
            var json = JsonConvert.SerializeObject(new
            {
                meetingId = meetingId,
                senderId = senderId,
                recipientId = recipientId,
                content = content,
                type = 0, // Text
                scope = recipientId.HasValue ? 1 : 0 // Private if recipient, otherwise Public
            });
            var contentObj = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync("/api/chat/send", contentObj);

            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                return JsonConvert.DeserializeObject<ChatMessageDto>(responseContent);
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending chat message in meeting {MeetingId}", meetingId);
            return null;
        }
    }
}
