using GMCadiomMeeting.Shared.Enums;
using GMCadiomMeeting.Shared.ViewModels.Meeting;
using GMCadiomMeeting.Shared.ViewModels.Participant;
using GMCadiomMeeting.Shared.ViewModels.User;
using System.ComponentModel.DataAnnotations;

namespace GMCadiomMeeting.Web.Models;

/// <summary>
/// Web-specific ViewModels that extend or adapt the shared models for MVC views
/// These models are specifically designed for web forms and UI binding
/// </summary>

#region Authentication ViewModels

/// <summary>
/// ViewModel for user login form
/// </summary>
public class LoginViewModel
{
    [Required(ErrorMessage = "Email is required")]
    [EmailAddress(ErrorMessage = "Invalid email format")]
    [Display(Name = "Email Address")]
    public string Email { get; set; } = string.Empty;

    [Required(ErrorMessage = "Password is required")]
    [DataType(DataType.Password)]
    [Display(Name = "Password")]
    public string Password { get; set; } = string.Empty;

    [Display(Name = "Remember me")]
    public bool RememberMe { get; set; }
}

/// <summary>
/// ViewModel for user registration form
/// </summary>
public class RegisterViewModel
{
    [Required(ErrorMessage = "Email is required")]
    [EmailAddress(ErrorMessage = "Invalid email format")]
    [Display(Name = "Email Address")]
    public string Email { get; set; } = string.Empty;

    [Required(ErrorMessage = "Password is required")]
    [DataType(DataType.Password)]
    [StringLength(100, MinimumLength = 6, ErrorMessage = "Password must be between 6 and 100 characters")]
    [Display(Name = "Password")]
    public string Password { get; set; } = string.Empty;

    [Required(ErrorMessage = "Display name is required")]
    [StringLength(100, MinimumLength = 2, ErrorMessage = "Display name must be between 2 and 100 characters")]
    [Display(Name = "Display Name")]
    public string DisplayName { get; set; } = string.Empty;

    [StringLength(50, ErrorMessage = "First name cannot exceed 50 characters")]
    [Display(Name = "First Name")]
    public string? FirstName { get; set; }

    [StringLength(50, ErrorMessage = "Last name cannot exceed 50 characters")]
    [Display(Name = "Last Name")]
    public string? LastName { get; set; }

    [Display(Name = "Time Zone")]
    public string? TimeZone { get; set; }
}



#endregion

#region Meeting ViewModels

/// <summary>
/// ViewModel for creating a new meeting
/// </summary>
public class CreateMeetingViewModel
{
    [Required(ErrorMessage = "Meeting title is required")]
    [StringLength(200, MinimumLength = 3, ErrorMessage = "Title must be between 3 and 200 characters")]
    [Display(Name = "Meeting Title")]
    public string Title { get; set; } = string.Empty;

    [StringLength(1000, ErrorMessage = "Description cannot exceed 1000 characters")]
    [Display(Name = "Description")]
    [DataType(DataType.MultilineText)]
    public string? Description { get; set; }

    [Required(ErrorMessage = "Meeting type is required")]
    [Display(Name = "Meeting Type")]
    public MeetingType Type { get; set; } = MeetingType.Group;

    [StringLength(50, ErrorMessage = "Password cannot exceed 50 characters")]
    [Display(Name = "Meeting Password")]
    [DataType(DataType.Password)]
    public string? Password { get; set; }

    [Range(2, 1000, ErrorMessage = "Maximum participants must be between 2 and 1000")]
    [Display(Name = "Maximum Participants")]
    public int MaxParticipants { get; set; } = 100;

    [Required(ErrorMessage = "Start time is required")]
    [Display(Name = "Start Time")]
    [DataType(DataType.DateTime)]
    public DateTime ScheduledStartTime { get; set; } = DateTime.Now.AddHours(1);

    [Required(ErrorMessage = "End time is required")]
    [Display(Name = "End Time")]
    [DataType(DataType.DateTime)]
    public DateTime ScheduledEndTime { get; set; } = DateTime.Now.AddHours(2);

    [Display(Name = "Enable Recording")]
    public bool IsRecordingEnabled { get; set; }

    [Display(Name = "Recurring Meeting")]
    public bool IsRecurring { get; set; }

    [Display(Name = "Recurrence Pattern")]
    public string? RecurrencePattern { get; set; }
}
#endregion

#region Invitation ViewModels

/// <summary>
/// ViewModel for sending meeting invitations
/// </summary>
public class SendInvitationViewModel
{
    [Required(ErrorMessage = "Meeting ID is required")]
    public int MeetingId { get; set; }

    [Required(ErrorMessage = "At least one email address is required")]
    [Display(Name = "Email Addresses")]
    [DataType(DataType.MultilineText)]
    public string EmailAddresses { get; set; } = string.Empty;

    [Display(Name = "Personal Message")]
    [StringLength(1000, ErrorMessage = "Personal message cannot exceed 1000 characters")]
    [DataType(DataType.MultilineText)]
    public string? PersonalMessage { get; set; }

    [Display(Name = "Invitation Role")]
    public ParticipantRole InvitedRole { get; set; } = ParticipantRole.Attendee;

    [Display(Name = "Expires At")]
    [DataType(DataType.DateTime)]
    public DateTime? ExpiresAt { get; set; }

    [Display(Name = "Send Reminder")]
    public bool SendReminder { get; set; } = true;

    [Display(Name = "Allow Join Before Host")]
    public bool AllowJoinBeforeHost { get; set; } = false;
}

#endregion

#region Dashboard ViewModels

/// <summary>
/// ViewModel for the user dashboard
/// </summary>
public class DashboardViewModel
{
    public UserDto User { get; set; } = new();
    public List<MeetingDto> UpcomingMeetings { get; set; } = new();
    public List<MeetingDto> ActiveMeetings { get; set; } = new();
    public List<InvitationDto> PendingInvitations { get; set; } = new();
    public DashboardStats Stats { get; set; } = new();
}

/// <summary>
/// Dashboard statistics
/// </summary>
public class DashboardStats
{
    public int TotalMeetings { get; set; }
    public int UpcomingMeetings { get; set; }
    public int ActiveMeetings { get; set; }
    public int CompletedMeetings { get; set; }
    public int PendingInvitations { get; set; }
    public int TotalParticipants { get; set; }
    public int TotalMinutesInMeetings { get; set; }
}

#endregion

#region Utility ViewModels



/// <summary>
/// ViewModel for pagination
/// </summary>
public class PaginationViewModel
{
    public int CurrentPage { get; set; }
    public int TotalPages { get; set; }
    public int PageSize { get; set; }
    public int TotalItems { get; set; }
    public bool HasPreviousPage => CurrentPage > 1;
    public bool HasNextPage => CurrentPage < TotalPages;
    public string? SearchTerm { get; set; }
    public string? SortBy { get; set; }
    public string? SortDirection { get; set; }
}

/// <summary>
/// ViewModel for select list items
/// </summary>
public class SelectListItemViewModel
{
    public string Value { get; set; } = string.Empty;
    public string Text { get; set; } = string.Empty;
    public bool Selected { get; set; }
    public bool Disabled { get; set; }
}

#endregion
