using GMCadiomMeeting.Shared.Enums;
using System.ComponentModel.DataAnnotations;

namespace GMCadiomMeeting.Shared.ViewModels.Meeting.CreateMeeting;

/// <summary>
/// Request model for creating a new meeting
/// </summary>
public class CreateMeetingRequest
{
    public int HostUserId { get; set; }

    /// <summary>
    /// Meeting title
    /// </summary>
    [Required(ErrorMessage = "Meeting title is required")]
    [StringLength(200, MinimumLength = 3, ErrorMessage = "Title must be between 3 and 200 characters")]
    public string Title { get; set; } = string.Empty;

    /// <summary>
    /// Meeting description
    /// </summary>
    [StringLength(1000, ErrorMessage = "Description cannot exceed 1000 characters")]
    public string? Description { get; set; }

    /// <summary>
    /// Meeting type
    /// </summary>
    [Required(ErrorMessage = "Meeting type is required")]
    public MeetingType Type { get; set; }

    /// <summary>
    /// Meeting password (optional)
    /// </summary>
    [StringLength(50, ErrorMessage = "Password cannot exceed 50 characters")]
    public string? Password { get; set; }

    /// <summary>
    /// Maximum number of participants allowed
    /// </summary>
    [Range(2, 1000, ErrorMessage = "Maximum participants must be between 2 and 1000")]
    public int MaxParticipants { get; set; } = 100;

    /// <summary>
    /// Scheduled start time
    /// </summary>
    [Required(ErrorMessage = "Scheduled start time is required")]
    public DateTime ScheduledStartTime { get; set; }

    /// <summary>
    /// Scheduled end time
    /// </summary>
    [Required(ErrorMessage = "Scheduled end time is required")]
    public DateTime ScheduledEndTime { get; set; }

    /// <summary>
    /// Indicates if recording is enabled
    /// </summary>
    public bool IsRecordingEnabled { get; set; }

    /// <summary>
    /// Indicates if the meeting is recurring
    /// </summary>
    public bool IsRecurring { get; set; }

    /// <summary>
    /// Recurrence pattern (if recurring)
    /// </summary>
    [StringLength(100, ErrorMessage = "Recurrence pattern cannot exceed 100 characters")]
    public string? RecurrencePattern { get; set; }
}
