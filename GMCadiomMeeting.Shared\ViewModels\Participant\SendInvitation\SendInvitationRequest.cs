using GMCadiomMeeting.Shared.Enums;
using System.ComponentModel.DataAnnotations;

namespace GMCadiomMeeting.Shared.ViewModels.Participant.SendInvitation;

/// <summary>
/// Request model for sending meeting invitations
/// </summary>
public class SendInvitationRequest
{
    /// <summary>
    /// Meeting ID
    /// </summary>
    [Required(ErrorMessage = "Meeting ID is required")]
    public int MeetingId { get; set; }

    /// <summary>
    /// List of email addresses to invite
    /// </summary>
    [Required(ErrorMessage = "At least one email address is required")]
    [MinLength(1, ErrorMessage = "At least one email address is required")]
    public List<string> EmailAddresses { get; set; } = new();

    /// <summary>
    /// Role to assign to invited participants
    /// </summary>
    public ParticipantRole InvitedRole { get; set; } = ParticipantRole.Attendee;

    /// <summary>
    /// Personal message to include with the invitation
    /// </summary>
    [StringLength(1000, ErrorMessage = "Personal message cannot exceed 1000 characters")]
    public string? PersonalMessage { get; set; }

    /// <summary>
    /// Method to use for sending invitations
    /// </summary>
    public InvitationMethod Method { get; set; } = InvitationMethod.Email;

    /// <summary>
    /// Expiration date for the invitations
    /// </summary>
    public DateTime? ExpiresAt { get; set; }

    /// <summary>
    /// Send reminder before meeting
    /// </summary>
    public bool SendReminder { get; set; } = true;
}
